/**
 * Electron API 类型定义
 * 可导出的类型接口，供其他模块导入使用
 */

export interface LoggerAPI {
  'logger:info': (message: string, meta?: any) => Promise<void>
  'logger:warn': (message: string, meta?: any) => Promise<void>
  'logger:error': (message: string, error?: any, meta?: any) => Promise<void>
  'logger:debug': (message: string, meta?: any) => Promise<void>
}

export interface WindowAPI {
  'window:minimize': () => Promise<void>
  'window:maximize': () => Promise<void>
  'window:close': () => Promise<void>
  'window:get-state': () => Promise<any>
  'window:set-size': (width: number, height: number) => Promise<void>
  'window:center': () => Promise<void>
}

export interface DialogAPI {
  'dialog:select-file': (options?: any) => Promise<string | null>
  'dialog:select-folder': () => Promise<string | null>
  'dialog:save-file': (options?: any) => Promise<string | null>
  'dialog:show-message': (message: string, type?: 'info' | 'warning' | 'error') => Promise<void>
}

export interface AppAPI {
  'app:get-version': () => Promise<string>
  'app:get-info': () => Promise<any>
  'app:quit': () => Promise<void>
  'app:restart': () => Promise<void>
}

export interface MattVerseAPI {
  'mattverse:get-config': () => Promise<{
    appName: string
    version: string
    features: string[]
  }>
  getConfig: () => Promise<{
    appName: string
    version: string
    features: string[]
  }>
  platform: string
  versions: {
    node: string
    electron: string
    chrome: string
    [key: string]: string
  }
  mattverse: {
    getWorkflows: () => Promise<any>
    saveWorkflow: (workflow: any) => Promise<any>
    deleteWorkflow: (id: string) => Promise<any>
  }
}

export interface StoreAPI {
  'store:get': (key: string) => Promise<any>
  'store:set': (key: string, value: any) => Promise<void>
  'store:delete': (key: string) => Promise<void>
  'store:clear': () => Promise<void>
  'store:has': (key: string) => Promise<boolean>
}

export interface FileSystemAPI {
  'fs:read-file': (path: string) => Promise<string>
  'fs:write-file': (path: string, content: string) => Promise<void>
  'fs:exists': (path: string) => Promise<boolean>
  'fs:mkdir': (path: string) => Promise<void>
}

export interface EventAPI {
  on: (channel: string, callback: (...args: any[]) => void) => () => void
  once: (channel: string, callback: (...args: any[]) => void) => void
  send: (channel: string, ...args: any[]) => void
  removeAllListeners: (channel: string) => void
}

export interface MiddlewareAPI {
  getConfig: () => Promise<{ success: boolean; data?: any; error?: string }>
  updateConfig: (host: string, port: number) => Promise<{ success: boolean; error?: string }>
  getVersion: () => Promise<{ success: boolean; data?: any; error?: string }>
  testConnection: (
    host?: string,
    port?: number
  ) => Promise<{ success: boolean; connected?: boolean; error?: string }>
  restartApp: () => Promise<{
    success: boolean
    isDevelopment?: boolean
    message?: string
    error?: string
  }>
}

export interface GrpcAPI {
  getStatus: () => Promise<{ connected: boolean; error?: string }>
}

// 完整的 ElectronAPI 接口
export interface ElectronAPI extends
  LoggerAPI,
  WindowAPI,
  DialogAPI,
  AppAPI,
  StoreAPI,
  FileSystemAPI,
  MattVerseAPI {
  
  // 中台和 gRPC API
  middleware: MiddlewareAPI
  grpc: GrpcAPI
  
  // 事件 API
  events: EventAPI
  
  // 通用调用方法
  invoke: (channel: string, ...args: any[]) => Promise<any>
  send: (channel: string, ...args: any[]) => void
  on: (channel: string, callback: (...args: any[]) => void) => () => void
  once: (channel: string, callback: (...args: any[]) => void) => void
}
