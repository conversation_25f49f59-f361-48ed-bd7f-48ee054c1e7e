/**
 * IPC 处理器工厂
 * 提供统一的 IPC 处理器注册和管理
 */
import { ipcMain, BrowserWindow, app, dialog } from 'electron'
import { IPCChannelName, IPCChannelHandler, WindowState, FileDialogOptions, SaveDialogOptions } from '../types/ipc'
import { logger } from '../utils/logger'

export class IPCHandlerFactory {
  private static registeredChannels = new Set<string>()

  /**
   * 注册单个 IPC 处理器
   */
  static register(
    channel: IPCChannelName,
    handler: IPCChannelHandler
  ): void {
    if (this.registeredChannels.has(channel)) {
      logger.warn(`IPC channel '${channel}' is already registered`)
      return
    }

    ipcMain.handle(channel, async (_event, ...args) => {
      try {
        logger.debug(`IPC call: ${channel}`, args)
        const result = await handler(...args)
        logger.debug(`IPC result: ${channel}`, result)
        return result
      } catch (error) {
        logger.error(`IPC error in ${channel}:`, error)
        throw error
      }
    })

    this.registeredChannels.add(channel)
    logger.info(`IPC handler registered: ${channel}`)
  }

  /**
   * 批量注册 IPC 处理器
   */
  static registerAll(handlers: Record<string, IPCChannelHandler>): void {
    Object.entries(handlers).forEach(([channel, handler]) => {
      if (handler) {
        this.register(channel, handler)
      }
    })
  }

  /**
   * 获取默认的 IPC 处理器
   */
  static getDefaultHandlers(): Record<string, IPCChannelHandler> {
    return {
      // App 相关
      'app:get-version': () => app.getVersion(),
      'app:get-info': () => ({
        name: app.getName(),
        version: app.getVersion(),
        platform: process.platform,
        arch: process.arch
      }),
      'app:quit': () => app.quit(),
      'app:restart': () => app.relaunch(),

      // Window 相关
      'window:minimize': () => {
        const window = BrowserWindow.getFocusedWindow()
        window?.minimize()
      },
      'window:maximize': () => {
        const window = BrowserWindow.getFocusedWindow()
        if (window?.isMaximized()) {
          window.unmaximize()
        } else {
          window?.maximize()
        }
      },
      'window:close': () => {
        const window = BrowserWindow.getFocusedWindow()
        window?.close()
      },
      'window:get-state': (): WindowState | null => {
        const window = BrowserWindow.getFocusedWindow()
        if (!window) return null
        
        return {
          isMaximized: window.isMaximized(),
          isMinimized: window.isMinimized(),
          isFullScreen: window.isFullScreen(),
          bounds: window.getBounds()
        }
      },
      'window:set-size': (width: number, height: number) => {
        const window = BrowserWindow.getFocusedWindow()
        window?.setSize(width, height)
      },
      'window:center': () => {
        const window = BrowserWindow.getFocusedWindow()
        window?.center()
      },

      // Dialog 相关
      'dialog:select-file': async (options: FileDialogOptions = {}): Promise<string | null> => {
        const result = await dialog.showOpenDialog({
          properties: ['openFile'],
          ...options
        })
        return result.canceled ? null : result.filePaths[0]
      },
      'dialog:select-folder': async (): Promise<string | null> => {
        const result = await dialog.showOpenDialog({
          properties: ['openDirectory']
        })
        return result.canceled ? null : result.filePaths[0]
      },
      'dialog:save-file': async (options: SaveDialogOptions = {}): Promise<string | null> => {
        const result = await dialog.showSaveDialog(options)
        return result.canceled ? null : result.filePath
      },
      'dialog:show-message': async (message: string, type: 'info' | 'warning' | 'error' = 'info'): Promise<void> => {
        await dialog.showMessageBox({
          type,
          message,
          buttons: ['OK']
        })
      },

      // Logger 相关
      'logger:info': (message: string, meta?: any): void => {
        logger.info(`[Renderer] ${message}`, meta)
      },
      'logger:warn': (message: string, meta?: any): void => {
        logger.warn(`[Renderer] ${message}`, meta)
      },
      'logger:error': (message: string, error?: any, meta?: any): void => {
        logger.error(`[Renderer] ${message}`, error, meta)
      },
      'logger:debug': (message: string, meta?: any): void => {
        logger.debug(`[Renderer] ${message}`, meta)
      }
    }
  }

  /**
   * 移除 IPC 处理器
   */
  static unregister(channel: IPCChannelName): void {
    ipcMain.removeHandler(channel)
    this.registeredChannels.delete(channel)
    logger.info(`IPC handler unregistered: ${channel}`)
  }

  /**
   * 清除所有注册的处理器
   */
  static clear(): void {
    this.registeredChannels.forEach(channel => {
      ipcMain.removeHandler(channel)
    })
    this.registeredChannels.clear()
    logger.info('All IPC handlers cleared')
  }
}
