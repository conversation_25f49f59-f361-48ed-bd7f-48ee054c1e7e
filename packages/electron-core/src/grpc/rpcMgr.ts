/**
 * gRPC 客户端 - 专注于 LinkerService
 */
import * as grpc from '@grpc/grpc-js'
import * as protoLoader from '@grpc/proto-loader'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { logger } from '../utils/logger.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

export interface GrpcClientOptions {
  host: string
  port: number
  secure?: boolean
  options?: Record<string, any>
}

export interface CallOptions {
  timeout?: number
  allowRetry?: boolean
  callType?: 'default' | 'serverStream' | 'clientStream'
}

/**
 * LinkerService gRPC 客户端
 */
export class LinkerGrpcClient {
  private linkerUrl: string
  private user_id: string
  private token: string
  private grpcClientOptions: Record<string, any>
  private linker: any
  private connected: boolean = false

  constructor(options: GrpcClientOptions, user_id: string, token: string) {
    this.linkerUrl = `${options.host}:${options.port}`
    this.user_id = user_id
    this.token = token
    this.grpcClientOptions = {
      'grpc.max_send_message_length': 100 * 1024 * 1024,
      'grpc.max_receive_message_length': 100 * 1024 * 1024,
      ...options.options
    }
    this.connect()
  }

  /**
   * 更新认证信息
   */
  updateAuthInfo(userId: string, token: string): boolean {
    if (userId && token) {
      this.user_id = userId
      this.token = token
      logger.info(`认证信息已更新: userId=${userId}`)
      return true
    }
    return false
  }

  /**
   * 核心调用方法 - 所有其他方法都基于此实现
   */
  call(
    apiName: string,
    params: Record<string, any>,
    callback: (error: any, response?: any, type?: string) => void,
    options: CallOptions = {}
  ): any {
    const { allowRetry = true, callType = 'default' } = options

    try {
      if (!this.linker) {
        logger.warn('linker 未连接，尝试重新连接')
        this.connect()
        if (!this.linker) {
          logger.error('linker 重连失败')
          callback(new Error('linker 未连接或连接失败'), null)
          return
        }
      }

      if (typeof this.linker[apiName] !== 'function') {
        logger.error(`接口 ${apiName} 不存在，请检查 linker.proto 文件`)
        callback(new Error(`接口 ${apiName} 不存在`), null)
        return
      }

      // 自动添加认证信息
      params.user_id = this.user_id
      params.token = this.token

      const metadata = new grpc.Metadata()
      metadata.add('method', apiName)
      metadata.add('source', 'client')

      logger.debug(`调用 ${apiName}，参数:`, params)

      if (callType === 'default') {
        this.linker[apiName](params, metadata, (error: any, response: any) =>
          this.preCallBack(error, response, apiName, params, callback, allowRetry)
        )
      } else if (callType === 'serverStream') {
        const service = this.linker[apiName](params, metadata)

        service.on('data', (response: any) => {
          callback(null, response, 'data')
        })

        service.on('end', () => {
          callback(null, undefined, 'end')
        })

        service.on('error', (error: any) => {
          callback(error, undefined, 'error')
        })

        return service
      } else if (callType === 'clientStream') {
        const stream = this.linker[apiName]((error: any, response: any) => {
          if (error) {
            callback(error, undefined, 'error')
          } else if (response) {
            callback(null, response, 'data')
          }
        })

        stream.on('end', () => {
          callback(null, undefined, 'end')
        })

        stream.on('error', (error: any) => {
          callback(error, undefined, 'error')
        })

        return stream
      }
    } catch (error) {
      logger.error(`调用 ${apiName} 时发生异常:`, error)
      callback(error, null)
    }
  }

  /**
   * 连接到 gRPC 服务器
   */
  connect(): void {
    try {
      logger.info(`尝试连接 gRPC 服务器: ${this.linkerUrl}`)

      if (!this.linkerUrl || this.linkerUrl.trim() === '') {
        logger.error(`无效的服务器地址: "${this.linkerUrl}"`)
        return
      }

      // 使用固定的 proto 文件路径
      const PROTO_PATH = join(__dirname, 'protos/linker.proto')

      let fs: any
      try {
        fs = require('fs')
      } catch (error) {
        logger.error('无法加载 fs 模块:', error)
        return
      }

      // 检查 proto 文件是否存在
      if (!fs.existsSync(PROTO_PATH)) {
        logger.error(`找不到 linker.proto 文件: ${PROTO_PATH}`)
        return
      }

      logger.info(`加载 proto 文件: ${PROTO_PATH}`)

      const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
      })

      const protoDescriptor = grpc.loadPackageDefinition(packageDefinition)
      const linkerProto = (protoDescriptor as any).linker

      if (!linkerProto || !linkerProto.LinkerService) {
        logger.error('无法获取 LinkerService，可能由于 .proto 文件加载失败')
        return
      }

      this.linker = new linkerProto.LinkerService(
        this.linkerUrl,
        grpc.credentials.createInsecure(),
        this.grpcClientOptions
      )

      // 检查连接状态
      const channel = this.linker.getChannel()
      const state = channel.getConnectivityState(true)
      logger.debug(
        `连接状态: ${state} (0=IDLE, 1=CONNECTING, 2=READY, 3=TRANSIENT_FAILURE, 4=SHUTDOWN)`
      )

      if (state === grpc.connectivityState.READY) {
        logger.info(`成功连接到 ${this.linkerUrl}`)
        this.connected = true
      } else {
        logger.warn(`连接未就绪，状态: ${state}`)
        this.connected = false
      }
    } catch (error) {
      logger.error(`连接 ${this.linkerUrl} 失败:`, error)
      this.connected = false
    }
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    if (!this.linker) return false
    try {
      const channel = this.linker.getChannel()
      const state = channel.getConnectivityState(true)
      this.connected = state === grpc.connectivityState.READY
      return this.connected
    } catch (error) {
      logger.error('检查连接状态失败:', error)
      this.connected = false
      return false
    }
  }

  /**
   * 获取当前连接的URL
   */
  getLinkerUrl(): string {
    return this.linkerUrl
  }

  /**
   * 获取当前用户ID
   */
  getUserId(): string {
    return this.user_id
  }

  /**
   * 预处理回调 - 处理重连逻辑
   */
  private preCallBack(
    error: any,
    response: any,
    apiName: string,
    params: any,
    callback: Function,
    allowRetry: boolean
  ): void {
    if (error && error.toString().startsWith('Error: 14 UNAVAILABLE')) {
      logger.error(`gRPC 调用 ${apiName} 失败: 服务不可用`, error)
      this.connect()
      if (allowRetry) {
        this.call(apiName, params, callback as any, { allowRetry: false })
      } else {
        callback(error, null)
      }
    } else {
      callback(error, response)
    }
  }


  /**
   * Ping 测试
   */
  ping(callback: (error: any, response?: any) => void): void {
    this.call('ping', { timestamp: Date.now() }, callback)
  }


}
