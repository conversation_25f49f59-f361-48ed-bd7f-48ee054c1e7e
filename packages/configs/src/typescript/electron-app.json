{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2020", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "useDefineForClassFields": true, "jsx": "preserve", "jsxImportSource": "vue", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": false, "allowJs": true, "checkJs": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "skipLibCheck": true, "verbatimModuleSyntax": false, "baseUrl": ".", "paths": {"@/*": ["./src/renderer/src/*"], "@main/*": ["./src/main/*"], "@preload/*": ["./src/preload/*"], "@mattverse/shared": ["../../packages/shared/src"], "@mattverse/mattverse-ui": ["../../packages/mattverse-ui/src"], "@mattverse/mattverse-flow": ["../../packages/mattverse-flow/src"], "@mattverse/electron-core": ["../../packages/electron-core/src"], "@mattverse/configs": ["../../packages/configs/src"]}}, "include": ["src/**/*", "electron.vite.config.ts", "electron-builder.yml", "../../shared/src/types/electron.d.ts"], "exclude": ["node_modules", "dist", "out", "release", "**/*.test.*", "**/*.spec.*"]}