/**
 * Mattverse 预加载脚本
 */
import { contextBridge, ipc<PERSON>enderer } from 'electron'
import { createPreloadBridge, logger } from '@mattverse/electron-core'

// 创建 Mattverse 特定的自定义 API
const customAPI = {
  // Mattverse 特定配置
  getConfig: () => ipcRenderer.invoke('mattverse:get-config'),

  // 系统信息
  platform: process.platform,
  arch: process.arch,
  versions: process.versions,

  // Mattverse 特定功能
  mattverse: {
    getWorkflows: () => ipcRenderer.invoke('mattverse:get-workflows'),
    saveWorkflow: (workflow: any) => ipcRenderer.invoke('mattverse:save-workflow', workflow),
    deleteWorkflow: (id: string) => ipcRenderer.invoke('mattverse:delete-workflow', id)
  },

  // 中台配置相关 API
  middleware: {
    getConfig: () => ipcRenderer.invoke('middleware:get-config'),
    updateConfig: (host: string, port: number) => ipcRenderer.invoke('middleware:update-config', host, port),
    getVersion: () => ipcRenderer.invoke('middleware:get-version'),
    testConnection: (host?: string, port?: number) => ipcRenderer.invoke('middleware:test-connection', host, port),
    restartApp: () => ipcRenderer.invoke('middleware:restart-app'),
  },

  // gRPC 相关 API
  grpc: {
    init: () => ipcRenderer.invoke('grpc:init'),
    ping: () => ipcRenderer.invoke('grpc:ping'),
    call: (apiName: string, params: Record<string, any>) => ipcRenderer.invoke('grpc:call', apiName, params),
    getStatus: () => ipcRenderer.invoke('grpc:get-status'),
  }
}

// 使用 electron-core 的预加载桥接工厂
const api = createPreloadBridge(customAPI)

// 暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
    logger.info('Mattverse APIs exposed successfully')
  } catch (error) {
    logger.error('Failed to expose Mattverse APIs:', error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electronAPI = api
}

// 导出类型定义
export type ElectronAPI = typeof api
