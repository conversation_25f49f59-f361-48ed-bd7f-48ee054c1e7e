/**
 * Mattverse 主进程
 */
import { config } from 'dotenv'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { readFileSync, writeFileSync, existsSync } from 'node:fs'
import {
  createElectronApp,
  logger,
  createLinkerClient,
  type LinkerGrpcClient,
} from '@mattverse/electron-core'

// 加载环境变量
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '../../../..')
config({ path: join(rootDir, '.env.development') })

// 全局 gRPC 客户端
let grpcClient: LinkerGrpcClient | null = null

// 获取配置的函数 - 优先使用 middle.config.json，不存在时使用环境变量
function getMiddlewareConfig() {
  const configPath = join(__dirname, '..', '..', 'middle.config.json')

  // 检查 middle.config.json 文件是否存在
  if (existsSync(configPath)) {
    try {
      const configContent = readFileSync(configPath, 'utf-8')
      const config = JSON.parse(configContent)
      logger.info('使用 middle.config.json 配置:', config)
      return {
        host: config.host || '',
        port: config.port || null,
      }
    } catch (error) {
      logger.warn('读取 middle.config.json 失败，回退到环境变量:', error)
    }
  }

  // 回退到环境变量（从 .env.development 文件）
  const config = {
    host: process.env.VITE_APP_LINKER_HOST || '',
    port: parseInt(process.env.VITE_APP_LINKER_PORT || '') || null,
  }
  logger.info('使用环境变量配置:', config)
  return config
}

// 初始化 gRPC 客户端
function initGrpcClient() {
  try {
    const middlewareConfig = getMiddlewareConfig()
    const config = {
      host: middlewareConfig.host,
      port: middlewareConfig.port,
      secure: false,
    }

    const userId = process.env.VITE_APP_USER_ID || '0'
    const token = process.env.VITE_APP_USER_TOKEN || 'abcdefghijklmn'

    grpcClient = createLinkerClient(config, userId, token)

    logger.info('Mattverse gRPC 客户端初始化成功:', {
      host: config.host,
      port: config.port,
      userId,
      token: token,
    })

    return grpcClient
  } catch (error) {
    logger.error('Mattverse gRPC 客户端初始化失败:', error)
    return null
  }
}

// 创建 Mattverse 应用
const app = createElectronApp({
  window: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
    },
  },
  // 配置开发者工具
  devTools: {
    enabled: true,
    extensions: ['VUE_DEVTOOLS'], // 安装 Vue 开发者工具
    autoOpen: true, // 开发环境自动打开
    forceInProduction: false, // 生产环境不启用
  },
  handlers: {
    // Mattverse 特定的 IPC 处理器
    'mattverse:get-config': async () => {
      return {
        appName: 'Mattverse',
        version: '1.1.0',
        features: ['workflow', 'ai', 'automation'],
      }
    },

    // gRPC 相关处理器
    'grpc:init': async () => {
      try {
        if (!grpcClient) {
          grpcClient = initGrpcClient()
        }
        return {
          success: !!grpcClient,
          connected: grpcClient?.isConnected() || false,
          message: grpcClient ? 'gRPC 客户端已初始化' : 'gRPC 客户端初始化失败',
        }
      } catch (error) {
        logger.error('gRPC 初始化失败:', error)
        return {
          success: false,
          connected: false,
          error: error instanceof Error ? error.message : '初始化失败',
        }
      }
    },

    'grpc:ping': async () => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化',
          })
          return
        }

        grpcClient.ping((error, response) => {
          if (error) {
            logger.error('gRPC Ping 失败:', error)
            resolve({
              success: false,
              error: error.message,
            })
          } else {
            logger.info('gRPC Ping 成功:', response)
            resolve({
              success: true,
              data: response,
            })
          }
        })
      })
    },

    'grpc:call': async (apiName: string, params: Record<string, any>) => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化',
          })
          return
        }

        grpcClient.call(apiName, params, (error, response) => {
          if (error) {
            logger.error(`gRPC 调用 ${apiName} 失败:`, error)
            resolve({
              success: false,
              error: error.message,
            })
          } else {
            logger.info(`gRPC 调用 ${apiName} 成功:`, response)
            resolve({
              success: true,
              data: response,
            })
          }
        })
      })
    },

    'grpc:get-status': async () => {
      return {
        initialized: !!grpcClient,
        connected: grpcClient?.isConnected() || false,
        url: grpcClient?.getLinkerUrl() || null,
        userId: grpcClient?.getUserId() || null,
      }
    },

    // 中台配置相关处理器
    'middleware:get-config': async () => {
      try {
        const config = getMiddlewareConfig()
        return {
          success: true,
          data: config,
        }
      } catch (error) {
        logger.error('获取中台配置失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '获取配置失败',
        }
      }
    },

    'middleware:update-config': async (host: string, port: number) => {
      try {
        // 1. 更新根目录的 .env.development 文件
        const envPath = join(rootDir, '.env.development')
        let envContent = ''

        if (existsSync(envPath)) {
          envContent = readFileSync(envPath, 'utf-8')
        }

        // 更新或添加配置项的函数
        const updateEnvVar = (content: string, key: string, value: string) => {
          const lines = content.split('\n')
          let found = false

          // 查找并更新现有的配置项
          for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith(`${key}=`)) {
              lines[i] = `${key}=${value}`
              found = true
              break
            }
          }

          // 如果没有找到，添加新的配置项
          if (!found) {
            // 确保文件以换行符结尾
            if (content && !content.endsWith('\n')) {
              lines.push('')
            }
            lines.push(`${key}=${value}`)
          }

          return lines.join('\n')
        }

        // 更新 .env.development 文件中的配置项
        envContent = updateEnvVar(envContent, 'VITE_APP_LINKER_HOST', host)
        envContent = updateEnvVar(envContent, 'VITE_APP_LINKER_PORT', port.toString())

        // 确保文件以换行符结尾
        if (!envContent.endsWith('\n')) {
          envContent += '\n'
        }

        writeFileSync(envPath, envContent, 'utf-8')
        logger.info('已更新 .env.development 文件:', { host, port })

        // 2. 生成 apps/mattverse/middle.config.json 文件（作为优先配置）
        const configPath = join(__dirname, '..', '..', 'middle.config.json')
        const middlewareConfig = {
          host,
          port,
          updatedAt: new Date().toISOString(),
          source: 'user_modified', // 标记这是用户修改的配置
        }
        writeFileSync(configPath, JSON.stringify(middlewareConfig, null, 2), 'utf-8')
        logger.info('已生成 middle.config.json 文件（优先配置）')

        // 3. 重新加载环境变量（更新当前进程的环境变量）
        config({ path: join(rootDir, '.env.development'), override: true })
        logger.info('已重新加载环境变量')

        logger.info('中台配置已更新:', { host, port })

        return {
          success: true,
          message: '配置已保存，需要重启应用生效',
        }
      } catch (error) {
        logger.error('更新中台配置失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '更新配置失败',
        }
      }
    },

    'middleware:get-version': async () => {
      return new Promise((resolve) => {
        if (!grpcClient) {
          resolve({
            success: false,
            error: 'gRPC 客户端未初始化',
          })
          return
        }

        grpcClient.call('getVersion', {}, (error, response) => {
          if (error) {
            logger.error('获取中台版本失败:', error)
            resolve({
              success: false,
              error: error.message,
            })
          } else {
            logger.info('获取中台版本成功:', response)
            resolve({
              success: true,
              data: response,
            })
          }
        })
      })
    },

    'middleware:test-connection': async (host?: string, port?: number) => {
      try {
        // 如果提供了新的配置，创建临时客户端测试
        let testClient = grpcClient

        if (host && port) {
          const testConfig = {
            host,
            port,
            secure: false,
          }
          const userId = process.env.VITE_APP_USER_ID || '0'
          const token = process.env.VITE_APP_USER_TOKEN || 'abcdefghijklmn'
          testClient = createLinkerClient(testConfig, userId, token)
        }

        if (!testClient) {
          return {
            success: false,
            error: '无法创建测试客户端',
          }
        }

        return new Promise((resolve) => {
          testClient!.ping((error, response) => {
            if (error) {
              logger.error('连接测试失败:', error)
              resolve({
                success: false,
                connected: false,
                error: error.message,
              })
            } else {
              logger.info('连接测试成功:', response)
              resolve({
                success: true,
                connected: true,
                data: response,
              })
            }
          })
        })
      } catch (error) {
        logger.error('测试连接时发生异常:', error)
        return {
          success: false,
          connected: false,
          error: error instanceof Error ? error.message : '测试连接失败',
        }
      }
    },

    'middleware:restart-app': async () => {
      try {
        const isDevelopment = process.env.NODE_ENV === 'development'

        if (isDevelopment) {
          // 开发环境直接关闭应用
          logger.info('开发环境检测到，应用即将关闭...')
          setTimeout(() => {
            const { app } = require('electron')
            app.quit()
          }, 1000)

          return {
            success: true,
            isDevelopment: true,
            message: '应用将在1秒后关闭，请手动重启',
          }
        } else {
          // 生产环境自动重启
          logger.info('生产环境，应用即将自动重启...')
          setTimeout(() => {
            const { app } = require('electron')
            app.relaunch()
            app.exit()
          }, 1000)

          return {
            success: true,
            isDevelopment: false,
            message: '应用将在1秒后重启',
          }
        }
      } catch (error) {
        logger.error('重启应用失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '重启失败',
        }
      }
    },
  },
  onReady: () => {
    logger.info('Mattverse app is ready!')

    // 初始化 gRPC 客户端
    try {
      grpcClient = initGrpcClient()
      if (grpcClient) {
        logger.info('Mattverse gRPC 客户端已在应用启动时初始化')
      }
    } catch (error) {
      logger.error('Mattverse 应用启动时 gRPC 客户端初始化失败:', error)
    }
  },
  onWindowCreated: () => {
    logger.info('Mattverse window created!')
  },
})

// 启动应用
app.start().catch((error) => {
  logger.error('Failed to start Mattverse app:', error)
  process.exit(1)
})
