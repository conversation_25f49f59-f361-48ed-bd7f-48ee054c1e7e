/**
 * 主进程处理器统一导出
 */
import { grpcHandlers, initializeGrpcClient, setGrpcClient, getGrpcClient } from './grpc'
import { middlewareHandlers } from './middleware'
import { appHandlers } from './app'

/**
 * 合并所有处理器
 */
export const handlers = {
  ...grpcHandlers,
  ...middlewareHandlers,
  ...appHandlers,
}

/**
 * 导出 gRPC 相关工具函数
 */
export {
  initializeGrpcClient,
  setGrpcClient,
  getGrpcClient,
}

/**
 * 导出各个模块的处理器（如果需要单独使用）
 */
export {
  grpcHandlers,
  middlewareHandlers,
  appHandlers,
}
