/**
 * 中台配置相关处理器
 */
import { join, dirname } from 'node:path'
import { readFileSync, writeFileSync, existsSync } from 'node:fs'
import { fileURLToPath } from 'node:url'
import { config } from 'dotenv'
import { logger, createLinkerClient } from '@mattverse/electron-core'
import { getGrpcClient } from './grpc'

// 获取当前文件的路径信息
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '../../../..')

/**
 * 获取配置的函数 - 优先使用 middle.config.json，不存在时使用环境变量
 */
function getMiddlewareConfig() {
  const configPath = join(__dirname, '..', '..', '..', 'middle.config.json')

  // 检查 middle.config.json 文件是否存在
  if (existsSync(configPath)) {
    try {
      const configContent = readFileSync(configPath, 'utf-8')
      const configData = JSON.parse(configContent)
      logger.info('使用 middle.config.json 配置:', configData)
      return {
        host: configData.host || '',
        port: configData.port || null,
      }
    } catch (error) {
      logger.warn('读取 middle.config.json 失败，回退到环境变量:', error)
    }
  }

  // 回退到环境变量（从 .env.development 文件）
  const configData = {
    host: process.env.VITE_APP_LINKER_HOST || '',
    port: parseInt(process.env.VITE_APP_LINKER_PORT || '') || null,
  }
  logger.info('使用环境变量配置:', configData)
  return configData
}

/**
 * 中台配置相关的 IPC 处理器
 */
export const middlewareHandlers = {
  // 获取中台配置
  'middleware:get-config': async () => {
    try {
      const config = getMiddlewareConfig()
      return {
        success: true,
        data: config,
      }
    } catch (error) {
      logger.error('获取中台配置失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取配置失败',
      }
    }
  },

  // 更新中台配置
  'middleware:update-config': async (host: string, port: number) => {
    try {
      // 1. 更新根目录的 .env.development 文件
      const envPath = join(rootDir, '.env.development')
      let envContent = ''

      if (existsSync(envPath)) {
        envContent = readFileSync(envPath, 'utf-8')
      }

      // 更新或添加配置项的函数
      const updateEnvVar = (content: string, key: string, value: string) => {
        const lines = content.split('\n')
        let found = false

        // 查找并更新现有的配置项
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].startsWith(`${key}=`)) {
            lines[i] = `${key}=${value}`
            found = true
            break
          }
        }

        // 如果没有找到，添加新的配置项
        if (!found) {
          // 确保文件以换行符结尾
          if (content && !content.endsWith('\n')) {
            lines.push('')
          }
          lines.push(`${key}=${value}`)
        }

        return lines.join('\n')
      }

      // 更新 .env.development 文件中的配置项
      envContent = updateEnvVar(envContent, 'VITE_APP_LINKER_HOST', host)
      envContent = updateEnvVar(envContent, 'VITE_APP_LINKER_PORT', port.toString())

      // 确保文件以换行符结尾
      if (!envContent.endsWith('\n')) {
        envContent += '\n'
      }

      writeFileSync(envPath, envContent, 'utf-8')
      logger.info('已更新 .env.development 文件:', { host, port })

      // 2. 生成 apps/mattverse/middle.config.json 文件（作为优先配置）
      const configPath = join(__dirname, '..', '..', '..', 'middle.config.json')
      const middlewareConfig = {
        host,
        port,
        updatedAt: new Date().toISOString(),
        source: 'user_modified', // 标记这是用户修改的配置
      }
      writeFileSync(configPath, JSON.stringify(middlewareConfig, null, 2), 'utf-8')
      logger.info('已生成 middle.config.json 文件（优先配置）')

      // 3. 重新加载环境变量（更新当前进程的环境变量）
      config({ path: join(rootDir, '.env.development'), override: true })
      logger.info('已重新加载环境变量')

      logger.info('中台配置已更新:', { host, port })

      return {
        success: true,
        message: '配置已保存，需要重启应用生效',
      }
    } catch (error) {
      logger.error('更新中台配置失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新配置失败',
      }
    }
  },

  // 获取中台版本
  'middleware:get-version': async () => {
    return new Promise((resolve) => {
      const grpcClient = getGrpcClient()
      if (!grpcClient) {
        resolve({
          success: false,
          error: 'gRPC 客户端未初始化',
        })
        return
      }

      grpcClient.call('getVersion', {}, (error, response) => {
        if (error) {
          logger.error('获取中台版本失败:', error)
          resolve({
            success: false,
            error: error.message,
          })
        } else {
          logger.info('获取中台版本成功:', response)
          resolve({
            success: true,
            data: response,
          })
        }
      })
    })
  },

  // 测试中台连接
  'middleware:test-connection': async (host?: string, port?: number) => {
    try {
      // 如果提供了新的配置，创建临时客户端测试
      let testClient = getGrpcClient()

      if (host && port) {
        const testConfig = {
          host,
          port,
          secure: false,
        }
        const userId = process.env.VITE_APP_USER_ID || '0'
        const token = process.env.VITE_APP_USER_TOKEN || 'abcdefghijklmn'
        testClient = createLinkerClient(testConfig, userId, token)
      }

      if (!testClient) {
        return {
          success: false,
          error: '无法创建测试客户端',
        }
      }

      return new Promise((resolve) => {
        testClient!.ping((error, response) => {
          if (error) {
            logger.error('连接测试失败:', error)
            resolve({
              success: false,
              connected: false,
              error: error.message,
            })
          } else {
            logger.info('连接测试成功:', response)
            resolve({
              success: true,
              connected: true,
              data: response,
            })
          }
        })
      })
    } catch (error) {
      logger.error('测试连接时发生异常:', error)
      return {
        success: false,
        connected: false,
        error: error instanceof Error ? error.message : '测试连接失败',
      }
    }
  },

  // 重启应用
  'middleware:restart-app': async () => {
    try {
      const isDevelopment = process.env.NODE_ENV === 'development'

      if (isDevelopment) {
        // 开发环境直接关闭应用
        logger.info('开发环境检测到，应用即将关闭...')
        setTimeout(() => {
          const { app } = require('electron')
          app.quit()
        }, 1000)

        return {
          success: true,
          isDevelopment: true,
          message: '应用将在1秒后关闭，请手动重启',
        }
      } else {
        // 生产环境自动重启
        logger.info('生产环境，应用即将自动重启...')
        setTimeout(() => {
          const { app } = require('electron')
          app.relaunch()
          app.exit()
        }, 1000)

        return {
          success: true,
          isDevelopment: false,
          message: '应用将在1秒后重启',
        }
      }
    } catch (error) {
      logger.error('重启应用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '重启失败',
      }
    }
  },
}
