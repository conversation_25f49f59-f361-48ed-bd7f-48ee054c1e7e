<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <Label class="text-sm font-medium">{{ $t('settings.notifications') }}</Label>
    </div>

    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{
            $t('settings.notification_options.enable_notifications')
          }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.notification_options.enable_notifications_desc') }}
          </p>
        </div>
        <Switch
          :checked="notifications.enableNotifications"
          @update:checked="updateNotification('enableNotifications', $event)"
        />
      </div>

      <Separator />

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{
            $t('settings.notification_options.desktop_notifications')
          }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.notification_options.desktop_notifications_desc') }}
          </p>
        </div>
        <Switch
          :checked="notifications.desktopNotifications"
          :disabled="!notifications.enableNotifications"
          @update:checked="updateNotification('desktopNotifications', $event)"
        />
      </div>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{
            $t('settings.notification_options.sound_notifications')
          }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.notification_options.sound_notifications_desc') }}
          </p>
        </div>
        <Switch
          :checked="notifications.soundNotifications"
          :disabled="!notifications.enableNotifications"
          @update:checked="updateNotification('soundNotifications', $event)"
        />
      </div>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{
            $t('settings.notification_options.email_notifications')
          }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.notification_options.email_notifications_desc') }}
          </p>
        </div>
        <Switch
          :checked="notifications.emailNotifications"
          :disabled="!notifications.enableNotifications"
          @update:checked="updateNotification('emailNotifications', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore, type NotificationSettings } from '@/store'

const settingsStore = useSettingsStore()

const notifications = computed(() => settingsStore.notifications)

const updateNotification = (key: keyof NotificationSettings, value: boolean) => {
  settingsStore.updateNotifications({ [key]: value })
}
</script>
