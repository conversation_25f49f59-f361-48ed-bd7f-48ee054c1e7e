<template>
  <div class="space-y-6">
    <!-- 主题模式选择 -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label class="text-sm font-medium">{{ $t('settings.theme') }}</Label>
        <Button variant="ghost" size="sm" @click="toggleTheme">
          <MattIcon :name="currentThemeIcon" class="h-4 w-4" />
        </Button>
      </div>

      <div class="grid grid-cols-3 gap-3">
        <div v-for="themeOption in themeOptions" :key="themeOption.value" class="relative">
          <input
            :id="`theme-${themeOption.value}`"
            v-model="selectedTheme"
            :value="themeOption.value"
            type="radio"
            name="theme"
            class="peer sr-only"
          />
          <label
            :for="`theme-${themeOption.value}`"
            :class="[
              'group flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 p-4 transition-all duration-200 hover:shadow-md peer-checked:border-primary peer-checked:shadow-lg',
              // 根据主题类型应用不同的背景和边框样式
              themeOption.value === 'light'
                ? 'bg-gradient-to-br from-orange-50 via-yellow-25 to-orange-100 border-orange-200 hover:border-orange-300 peer-checked:bg-orange-50 peer-checked:border-orange-400'
                : themeOption.value === 'dark'
                  ? 'bg-gradient-to-br from-slate-800 via-slate-850 to-slate-900 border-slate-600 hover:border-slate-500 peer-checked:bg-slate-800 peer-checked:border-slate-400 text-slate-100'
                  : 'bg-gradient-to-br from-gray-100 via-gray-150 to-gray-200 border-gray-300 hover:border-gray-400 peer-checked:bg-gray-100 peer-checked:border-gray-500',
            ]"
          >
            <!-- 主题图标预览 -->
            <div
              class="mb-3 flex h-12 w-16 items-center justify-center rounded-lg shadow-sm bg-white/10 backdrop-blur-sm"
            >
              <MattIcon
                :name="themeOption.icon"
                :class="[
                  'h-5 w-5',
                  themeOption.value === 'light'
                    ? 'text-orange-600'
                    : themeOption.value === 'dark'
                      ? 'text-blue-300'
                      : 'text-gray-600',
                ]"
              />
            </div>

            <span
              :class="[
                'text-sm font-medium transition-colors peer-checked:text-primary',
                themeOption.value === 'dark'
                  ? 'text-slate-100 group-hover:text-slate-50'
                  : 'text-foreground group-hover:text-primary',
              ]"
            >
              {{ themeOption.label }}
            </span>

            <!-- 选中指示器 -->
            <div
              v-if="selectedTheme === themeOption.value"
              :class="[
                'absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full',
                themeOption.value === 'dark'
                  ? 'bg-blue-400 text-slate-900'
                  : 'bg-primary text-primary-foreground',
              ]"
            >
              <MattIcon name="Check" class="h-3 w-3" />
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- 主题色调选择 -->
    <div class="space-y-3">
      <Label class="text-sm font-medium">{{ $t('settings.theme_colors') }}</Label>
      <div class="grid grid-cols-3 gap-3">
        <div v-for="colorTheme in colorThemes" :key="colorTheme.value" class="relative">
          <input
            :id="`color-theme-${colorTheme.value}`"
            v-model="selectedColorTheme"
            :value="colorTheme.value"
            type="radio"
            name="colorTheme"
            class="peer sr-only"
          />
          <label
            :for="`color-theme-${colorTheme.value}`"
            class="group flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-gray-200 bg-card p-3 transition-all duration-200 hover:border-primary/50 hover:bg-accent hover:shadow-md peer-checked:border-primary peer-checked:bg-primary/5 peer-checked:shadow-lg dark:border-gray-700"
          >
            <!-- 颜色背景预览 -->
            <div
              class="mb-2 h-8 w-12 rounded-lg shadow-sm transition-all duration-200 group-hover:scale-105 relative overflow-hidden"
              :style="{
                backgroundColor: colorTheme.previewColor,
                boxShadow: `inset 0 1px 2px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.1)`,
              }"
            >
              <!-- 添加一些装饰性元素 -->
              <div
                class="absolute inset-0 opacity-20"
                :style="{
                  background: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3), transparent 50%)`,
                }"
              />
            </div>
            <span
              class="text-xs font-medium text-center transition-colors group-hover:text-primary peer-checked:text-primary"
            >
              {{ getColorThemeLabel(colorTheme.value) }}
            </span>

            <!-- 选中指示器 -->
            <div
              v-if="selectedColorTheme === colorTheme.value"
              class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-primary-foreground"
            >
              <MattIcon name="Check" class="h-2.5 w-2.5" />
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- 当前主题显示 -->
    <div v-if="currentTheme !== selectedTheme" class="rounded-lg bg-muted/50 p-3">
      <div class="flex items-center space-x-2 text-sm text-muted-foreground">
        <MattIcon :name="currentThemeIcon" class="h-4 w-4" />
        <span
          >{{ $t('settings.theme_options.current_display') }}: {{ getCurrentThemeLabel() }}</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type ThemeMode } from '@/store'

// 定义颜色主题类型
type ColorTheme =
  | 'city-light'
  | 'forest-light'
  | 'lake-light'
  | 'desert-light'
  | 'farm-light'
  | 'garden-light'
  | 'city-dark'
  | 'forest-dark'
  | 'lake-dark'
  | 'desert-dark'
  | 'farm-dark'
  | 'garden-dark'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const selectedTheme = computed({
  get: () => settingsStore.theme,
  set: (value: ThemeMode) => settingsStore.setTheme(value),
})

const selectedColorTheme = computed({
  get: () => settingsStore.colorTheme,
  set: (value: ColorTheme) => settingsStore.setColorTheme(value),
})

const currentTheme = computed(() => settingsStore.currentTheme)

const currentThemeIcon = computed(() => {
  const option = themeOptions.value.find((opt) => opt.value === currentTheme.value)
  return option?.icon || 'Monitor'
})

const themeOptions = computed(() => [
  {
    value: 'light' as ThemeMode,
    label: t('settings.theme_options.light'),
    icon: 'Sun',
  },
  {
    value: 'dark' as ThemeMode,
    label: t('settings.theme_options.dark'),
    icon: 'Moon',
  },
  {
    value: 'system' as ThemeMode,
    label: t('settings.theme_options.system'),
    icon: 'Monitor',
  },
])

const allColorThemes = [
  // 浅色主题
  {
    value: 'city-light',
    previewColor: '#d8e8e7',
    primary: '187 25.9% 87.5%',
    type: 'light',
  },
  {
    value: 'forest-light',
    previewColor: '#e0d6af',
    primary: '49 47.4% 78.2%',
    type: 'light',
  },
  {
    value: 'lake-light',
    previewColor: '#cbcad9',
    primary: '243 17.3% 82.2%',
    type: 'light',
  },
  {
    value: 'desert-light',
    previewColor: '#fdcdb0',
    primary: '26 96.2% 84.1%',
    type: 'light',
  },
  {
    value: 'farm-light',
    previewColor: '#c8d3ec',
    primary: '222 47.4% 85.3%',
    type: 'light',
  },
  {
    value: 'garden-light',
    previewColor: '#fee1b2',
    primary: '37 98.2% 85.1%',
    type: 'light',
  },
  // 深色主题
  {
    value: 'city-dark',
    previewColor: '#2a3b42',
    primary: '197 22.9% 21.2%',
    type: 'dark',
  },
  {
    value: 'forest-dark',
    previewColor: '#2c3320',
    primary: '79 22.4% 16.2%',
    type: 'dark',
  },
  {
    value: 'lake-dark',
    previewColor: '#1e2a4a',
    primary: '220 42.4% 20.2%',
    type: 'dark',
  },
  {
    value: 'desert-dark',
    previewColor: '#3d2b1f',
    primary: '26 32.2% 18.2%',
    type: 'dark',
  },
  {
    value: 'farm-dark',
    previewColor: '#2a2d1e',
    primary: '69 22.4% 15.2%',
    type: 'dark',
  },
  {
    value: 'garden-dark',
    previewColor: '#2d1f2a',
    primary: '315 22.4% 15.2%',
    type: 'dark',
  },
]

// 根据当前主题模式过滤色调选项
const colorThemes = computed(() => {
  const currentThemeMode = currentTheme.value
  return allColorThemes.filter((theme) => theme.type === currentThemeMode)
})

const getCurrentThemeLabel = () => {
  const option = themeOptions.value.find((opt) => opt.value === currentTheme.value)
  return option?.label || currentTheme.value
}

const toggleTheme = () => {
  settingsStore.toggleTheme()
}

const getColorThemeLabel = (themeValue: string) => {
  const colorThemeLabels: Record<string, string> = {
    'city-light': t('settings.color_themes.city-light'),
    'forest-light': t('settings.color_themes.forest-light'),
    'lake-light': t('settings.color_themes.lake-light'),
    'desert-light': t('settings.color_themes.desert-light'),
    'farm-light': t('settings.color_themes.farm-light'),
    'garden-light': t('settings.color_themes.garden-light'),
    'city-dark': t('settings.color_themes.city-dark'),
    'forest-dark': t('settings.color_themes.forest-dark'),
    'lake-dark': t('settings.color_themes.lake-dark'),
    'desert-dark': t('settings.color_themes.desert-dark'),
    'farm-dark': t('settings.color_themes.farm-dark'),
    'garden-dark': t('settings.color_themes.garden-dark'),
  }
  return colorThemeLabels[themeValue] || themeValue
}

// 监听主题模式变化，自动切换对应的色调
watch(currentTheme, (newTheme, oldTheme) => {
  if (newTheme !== oldTheme) {
    const currentColorTheme = selectedColorTheme.value
    const currentThemeBase = currentColorTheme.replace(/-light|-dark$/, '')
    const newColorTheme = `${currentThemeBase}-${newTheme}` as ColorTheme

    // 检查新的色调是否存在
    const themeExists = allColorThemes.some((theme) => theme.value === newColorTheme)
    if (themeExists) {
      selectedColorTheme.value = newColorTheme
    } else {
      // 如果不存在，使用默认的第一个色调
      const defaultTheme = allColorThemes.find((theme) => theme.type === newTheme)
      if (defaultTheme) {
        selectedColorTheme.value = defaultTheme.value as ColorTheme
      }
    }
  }
})
</script>
