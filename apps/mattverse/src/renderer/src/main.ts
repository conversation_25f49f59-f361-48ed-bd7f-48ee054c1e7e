import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { setupStore } from './store'
import { installI18n, setDocumentLang } from '@mattverse/i18n'
// 从设置状态获取语言配置
import { useSettingsStore } from '@/store'
// 导入应用样式（包含 Tailwind CSS 和组件库样式）
import './assets/styles/index.css'

const app = createApp(App)

// 设置状态管理
setupStore(app)

const settingsStore = useSettingsStore()

// 设置国际化，使用设置中保存的语言
installI18n(app, {
  locale: settingsStore.language,
})

// 设置文档语言
setDocumentLang(settingsStore.language)

// 应用字体设置
settingsStore.applyFontSettings(settingsStore.font)

app.use(router)

app.mount('#app')
